// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/createApi": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "创建基础api",
                "parameters": [
                    {
                        "description": "api路径, api中文描述, api组, 方法",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysApi"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建基础api",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/deleteApi": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "删除api",
                "parameters": [
                    {
                        "description": "ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysApi"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除api",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/deleteApisByIds": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "删除选中Api",
                "parameters": [
                    {
                        "description": "ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.IdsReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除选中Api",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/enterSyncApi": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "确认同步API",
                "responses": {
                    "200": {
                        "description": "确认同步API",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/freshCasbin": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "刷新casbin缓存",
                "responses": {
                    "200": {
                        "description": "刷新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/getAllApis": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "获取所有的Api 不分页",
                "responses": {
                    "200": {
                        "description": "获取所有的Api 不分页,返回包括api列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAPIListResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/getApiById": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "根据id获取api",
                "parameters": [
                    {
                        "description": "根据id获取api",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "根据id获取api,返回包括api详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAPIResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/getApiGroups": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "获取API分组",
                "responses": {
                    "200": {
                        "description": "获取API分组",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/getApiList": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "分页获取API列表",
                "parameters": [
                    {
                        "description": "分页获取API列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SearchApiParams"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取API列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/ignoreApi": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "IgnoreApi"
                ],
                "summary": "忽略API",
                "responses": {
                    "200": {
                        "description": "同步API",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/syncApi": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "同步API",
                "responses": {
                    "200": {
                        "description": "同步API",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/updateApi": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysApi"
                ],
                "summary": "修改基础api",
                "parameters": [
                    {
                        "description": "api路径, api中文描述, api组, 方法",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysApi"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改基础api",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/attachmentCategory/addCategory": {
            "post": {
                "security": [
                    {
                        "AttachmentCategory": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AddCategory"
                ],
                "summary": "添加媒体库分类",
                "parameters": [
                    {
                        "description": "媒体库分类数据",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaAttachmentCategory"
                        }
                    }
                ],
                "responses": {}
            }
        },
        "/attachmentCategory/deleteCategory": {
            "post": {
                "security": [
                    {
                        "AttachmentCategory": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DeleteCategory"
                ],
                "summary": "删除分类",
                "parameters": [
                    {
                        "description": "分类id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除分类",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/attachmentCategory/getCategoryList": {
            "get": {
                "security": [
                    {
                        "AttachmentCategory": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "GetCategoryList"
                ],
                "summary": "媒体库分类列表",
                "responses": {
                    "200": {
                        "description": "媒体库分类列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/example.ExaAttachmentCategory"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/copyAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "拷贝角色",
                "parameters": [
                    {
                        "description": "旧角色id, 新权限id, 新权限名, 新父角色id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/response.SysAuthorityCopyResponse"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "拷贝角色,返回包括系统角色详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAuthorityResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/createAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "权限id, 权限名, 父角色id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysAuthority"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建角色,返回包括系统角色详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAuthorityResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/deleteAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "description": "删除角色",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysAuthority"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除角色",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/getAuthorityList": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "分页获取角色列表",
                "parameters": [
                    {
                        "description": "页码, 每页大小",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PageInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取角色列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/setDataAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "设置角色资源权限",
                "parameters": [
                    {
                        "description": "设置角色资源权限",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysAuthority"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置角色资源权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authority/updateAuthority": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authority"
                ],
                "summary": "更新角色信息",
                "parameters": [
                    {
                        "description": "权限id, 权限名, 父角色id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysAuthority"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新角色信息,返回包括系统角色详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAuthorityResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authorityBtn/canRemoveAuthorityBtn": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityBtn"
                ],
                "summary": "设置权限按钮",
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authorityBtn/getAuthorityBtn": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityBtn"
                ],
                "summary": "获取权限按钮",
                "parameters": [
                    {
                        "description": "菜单id, 角色id, 选中的按钮id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SysAuthorityBtnReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回列表成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysAuthorityBtnRes"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/authorityBtn/setAuthorityBtn": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityBtn"
                ],
                "summary": "设置权限按钮",
                "parameters": [
                    {
                        "description": "菜单id, 角色id, 选中的按钮id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SysAuthorityBtnReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回列表成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/addFunc": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AddFunc"
                ],
                "summary": "增加方法",
                "parameters": [
                    {
                        "description": "增加方法",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AutoCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"创建成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/autoCode/createPackage": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePackage"
                ],
                "summary": "创建package",
                "parameters": [
                    {
                        "description": "创建package",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SysAutoCodePackageCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建package成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/createTemp": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodeTemplate"
                ],
                "summary": "自动代码模板",
                "parameters": [
                    {
                        "description": "创建自动代码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AutoCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"创建成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/autoCode/delPackage": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "删除package",
                "parameters": [
                    {
                        "description": "创建package",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除package成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/delSysHistory": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "删除回滚记录",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除回滚记录",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getColumn": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "获取当前表所有字段",
                "responses": {
                    "200": {
                        "description": "获取当前表所有字段",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getDB": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "获取当前所有数据库",
                "responses": {
                    "200": {
                        "description": "获取当前所有数据库",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getMeta": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "获取meta信息",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取meta信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getPackage": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePackage"
                ],
                "summary": "获取package",
                "responses": {
                    "200": {
                        "description": "创建package成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getSysHistory": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "查询回滚记录",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PageInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询回滚记录,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getTables": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "获取当前数据库所有表",
                "responses": {
                    "200": {
                        "description": "获取当前数据库所有表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/getTemplates": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePackage"
                ],
                "summary": "获取package",
                "responses": {
                    "200": {
                        "description": "创建package成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/initAPI": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePlugin"
                ],
                "summary": "打包插件",
                "responses": {
                    "200": {
                        "description": "打包插件成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/initMenu": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePlugin"
                ],
                "summary": "打包插件",
                "responses": {
                    "200": {
                        "description": "打包插件成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/installPlugin": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePlugin"
                ],
                "summary": "安装插件",
                "parameters": [
                    {
                        "type": "file",
                        "description": "this is a test file",
                        "name": "plug",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "安装插件成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "type": "object"
                                            }
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/preview": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodeTemplate"
                ],
                "summary": "预览创建后的代码",
                "parameters": [
                    {
                        "description": "预览创建代码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AutoCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "预览创建后的代码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/pubPlug": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCodePlugin"
                ],
                "summary": "打包插件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "插件名称",
                        "name": "plugName",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "打包插件成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/autoCode/rollback": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AutoCode"
                ],
                "summary": "回滚自动生成代码",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SysAutoHistoryRollBack"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "回滚自动生成代码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/base/captcha": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Base"
                ],
                "summary": "生成验证码",
                "responses": {
                    "200": {
                        "description": "生成验证码,返回包括随机数id,base64,验证码长度,是否开启验证码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysCaptchaResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/base/login": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Base"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "用户名, 密码, 验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.Login"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回包括用户信息,token,过期时间",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/casbin/UpdateCasbin": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Casbin"
                ],
                "summary": "更新角色api权限",
                "parameters": [
                    {
                        "description": "权限id, 权限模型列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CasbinInReceive"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新角色api权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/casbin/getPolicyPathByAuthorityId": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Casbin"
                ],
                "summary": "获取权限列表",
                "parameters": [
                    {
                        "description": "权限id, 权限模型列表",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CasbinInReceive"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取权限列表,返回包括casbin详情列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PolicyPathResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/customer/customer": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaCustomer"
                ],
                "summary": "获取单一客户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "客户名",
                        "name": "customerName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "客户手机号",
                        "name": "customerPhoneData",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "管理角色ID",
                        "name": "sysUserAuthorityID",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "管理ID",
                        "name": "sysUserId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取单一客户信息,返回包括客户详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.ExaCustomerResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaCustomer"
                ],
                "summary": "更新客户信息",
                "parameters": [
                    {
                        "description": "客户ID, 客户信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaCustomer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新客户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaCustomer"
                ],
                "summary": "创建客户",
                "parameters": [
                    {
                        "description": "客户用户名, 客户手机号码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaCustomer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建客户",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaCustomer"
                ],
                "summary": "删除客户",
                "parameters": [
                    {
                        "description": "客户ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaCustomer"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除客户",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/customer/customerList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaCustomer"
                ],
                "summary": "分页获取权限客户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取权限客户列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/email/emailTest": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "发送测试邮件",
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"发送成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/email/sendEmail": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "发送邮件",
                "parameters": [
                    {
                        "description": "发送邮件必须的参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/response.Email"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"发送成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/breakpointContinue": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "断点续传到服务器",
                "parameters": [
                    {
                        "type": "file",
                        "description": "an example for breakpoint resume, 断点续传示例",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "断点续传到服务器",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/deleteFile": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "删除文件",
                "parameters": [
                    {
                        "description": "传入文件里面id即可",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaFileUploadAndDownload"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除文件",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/findFile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "查找文件",
                "parameters": [
                    {
                        "type": "file",
                        "description": "Find the file, 查找文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查找文件,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.FileResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "创建文件",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传文件完成",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建文件,返回包括文件路径",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.FilePathResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/getFileList": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "分页文件列表",
                "parameters": [
                    {
                        "description": "页码, 每页大小, 分类id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.ExaAttachmentCategorySearch"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页文件列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/importURL": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "导入URL",
                "parameters": [
                    {
                        "description": "对象",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/example.ExaFileUploadAndDownload"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "导入URL",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/removeChunk": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "删除切片",
                "parameters": [
                    {
                        "type": "file",
                        "description": "删除缓存切片",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除切片",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/fileUploadAndDownload/upload": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "上传文件示例",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传文件示例",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传文件示例,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.ExaFileResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/createInfo": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "创建公告",
                "parameters": [
                    {
                        "description": "创建公告",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.Info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/deleteInfo": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "删除公告",
                "parameters": [
                    {
                        "description": "删除公告",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.Info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/deleteInfoByIds": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "批量删除公告",
                "responses": {
                    "200": {
                        "description": "批量删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/findInfo": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "用id查询公告",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "内容",
                        "name": "content",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "标题",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "作者",
                        "name": "userID",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/model.Info"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/getInfoDataSource": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "获取Info的数据源",
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/getInfoList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "分页获取公告列表",
                "parameters": [
                    {
                        "type": "string",
                        "name": "endCreatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "startCreatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/getInfoPublic": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "不需要鉴权的公告接口",
                "parameters": [
                    {
                        "type": "string",
                        "name": "endCreatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "startCreatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/info/updateInfo": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Info"
                ],
                "summary": "更新公告",
                "parameters": [
                    {
                        "description": "更新公告",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/model.Info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/init/checkdb": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "CheckDB"
                ],
                "summary": "初始化用户数据库",
                "responses": {
                    "200": {
                        "description": "初始化用户数据库",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/init/initdb": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "InitDB"
                ],
                "summary": "初始化用户数据库",
                "parameters": [
                    {
                        "description": "初始化数据库参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.InitDB"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "初始化用户数据库",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/jwt/jsonInBlacklist": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Jwt"
                ],
                "summary": "jwt加入黑名单",
                "responses": {
                    "200": {
                        "description": "jwt加入黑名单",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/addBaseMenu": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Menu"
                ],
                "summary": "新增菜单",
                "parameters": [
                    {
                        "description": "路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysBaseMenu"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "新增菜单",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/addMenuAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityMenu"
                ],
                "summary": "增加menu和角色关联关系",
                "parameters": [
                    {
                        "description": "角色ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.AddMenuAuthorityInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "增加menu和角色关联关系",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/deleteBaseMenu": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Menu"
                ],
                "summary": "删除菜单",
                "parameters": [
                    {
                        "description": "菜单id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除菜单",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/getBaseMenuById": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Menu"
                ],
                "summary": "根据id获取菜单",
                "parameters": [
                    {
                        "description": "菜单id",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "根据id获取菜单,返回包括系统菜单列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysBaseMenuResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/getBaseMenuTree": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityMenu"
                ],
                "summary": "获取用户动态路由",
                "parameters": [
                    {
                        "description": "空",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.Empty"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取用户动态路由,返回包括系统菜单列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysBaseMenusResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/getMenu": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityMenu"
                ],
                "summary": "获取用户动态路由",
                "parameters": [
                    {
                        "description": "空",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.Empty"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取用户动态路由,返回包括系统菜单详情列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysMenusResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/getMenuAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AuthorityMenu"
                ],
                "summary": "获取指定角色menu",
                "parameters": [
                    {
                        "description": "角色ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetAuthorityId"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取指定角色menu",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/getMenuList": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Menu"
                ],
                "summary": "分页获取基础menu列表",
                "parameters": [
                    {
                        "description": "页码, 每页大小",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.PageInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取基础menu列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/menu/updateBaseMenu": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Menu"
                ],
                "summary": "更新菜单",
                "parameters": [
                    {
                        "description": "路由path, 父菜单ID, 路由name, 对应前端文件路径, 排序标记",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysBaseMenu"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新菜单",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionary/createSysDictionary": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionary"
                ],
                "summary": "创建SysDictionary",
                "parameters": [
                    {
                        "description": "SysDictionary模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionary"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建SysDictionary",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionary/deleteSysDictionary": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionary"
                ],
                "summary": "删除SysDictionary",
                "parameters": [
                    {
                        "description": "SysDictionary模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionary"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除SysDictionary",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionary/findSysDictionary": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionary"
                ],
                "summary": "用id查询SysDictionary",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "描述",
                        "name": "desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "字典名（中）",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "字典名（英）",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用id查询SysDictionary",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionary/getSysDictionaryList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionary"
                ],
                "summary": "分页获取SysDictionary列表",
                "responses": {
                    "200": {
                        "description": "分页获取SysDictionary列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionary/updateSysDictionary": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionary"
                ],
                "summary": "更新SysDictionary",
                "parameters": [
                    {
                        "description": "SysDictionary模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionary"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新SysDictionary",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionaryDetail/createSysDictionaryDetail": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionaryDetail"
                ],
                "summary": "创建SysDictionaryDetail",
                "parameters": [
                    {
                        "description": "SysDictionaryDetail模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionaryDetail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建SysDictionaryDetail",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionaryDetail/deleteSysDictionaryDetail": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionaryDetail"
                ],
                "summary": "删除SysDictionaryDetail",
                "parameters": [
                    {
                        "description": "SysDictionaryDetail模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionaryDetail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除SysDictionaryDetail",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionaryDetail/findSysDictionaryDetail": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionaryDetail"
                ],
                "summary": "用id查询SysDictionaryDetail",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "扩展值",
                        "name": "extend",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "展示值",
                        "name": "label",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "排序标记",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "启用状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "关联标记",
                        "name": "sysDictionaryID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "字典值",
                        "name": "value",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用id查询SysDictionaryDetail",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionaryDetail/getSysDictionaryDetailList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionaryDetail"
                ],
                "summary": "分页获取SysDictionaryDetail列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "扩展值",
                        "name": "extend",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "展示值",
                        "name": "label",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "排序标记",
                        "name": "sort",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "启用状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "关联标记",
                        "name": "sysDictionaryID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "字典值",
                        "name": "value",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取SysDictionaryDetail列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysDictionaryDetail/updateSysDictionaryDetail": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysDictionaryDetail"
                ],
                "summary": "更新SysDictionaryDetail",
                "parameters": [
                    {
                        "description": "更新SysDictionaryDetail",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysDictionaryDetail"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新SysDictionaryDetail",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/ExportTemplate": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "导出表格模板",
                "responses": {}
            }
        },
        "/sysExportTemplate/createSysExportTemplate": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "创建导出模板",
                "parameters": [
                    {
                        "description": "创建导出模板",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysExportTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"创建成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/deleteSysExportTemplate": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "删除导出模板",
                "parameters": [
                    {
                        "description": "删除导出模板",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysExportTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"删除成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/deleteSysExportTemplateByIds": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "批量删除导出模板",
                "parameters": [
                    {
                        "description": "批量删除导出模板",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.IdsReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"批量删除成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/exportExcel": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "导出表格",
                "responses": {}
            }
        },
        "/sysExportTemplate/findSysExportTemplate": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "用id查询导出模板",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "数据库名称",
                        "name": "dbName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "order",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "表名称",
                        "name": "tableName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板标识",
                        "name": "templateID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板信息",
                        "name": "templateInfo",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"查询成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/getSysExportTemplateList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "分页获取导出模板列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "数据库名称",
                        "name": "dbName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "endCreatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "order",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "startCreatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "表名称",
                        "name": "tableName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板标识",
                        "name": "templateID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板信息",
                        "name": "templateInfo",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"获取成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysExportTemplate/importExcel": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysImportTemplate"
                ],
                "summary": "导入表格",
                "responses": {}
            }
        },
        "/sysExportTemplate/updateSysExportTemplate": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysExportTemplate"
                ],
                "summary": "更新导出模板",
                "parameters": [
                    {
                        "description": "更新导出模板",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysExportTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "{\"success\":true,\"data\":{},\"msg\":\"更新成功\"}",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/sysOperationRecord/createSysOperationRecord": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysOperationRecord"
                ],
                "summary": "创建SysOperationRecord",
                "parameters": [
                    {
                        "description": "创建SysOperationRecord",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysOperationRecord"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建SysOperationRecord",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysOperationRecord/deleteSysOperationRecord": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysOperationRecord"
                ],
                "summary": "删除SysOperationRecord",
                "parameters": [
                    {
                        "description": "SysOperationRecord模型",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysOperationRecord"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除SysOperationRecord",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysOperationRecord/deleteSysOperationRecordByIds": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysOperationRecord"
                ],
                "summary": "批量删除SysOperationRecord",
                "parameters": [
                    {
                        "description": "批量删除SysOperationRecord",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.IdsReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "批量删除SysOperationRecord",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysOperationRecord/findSysOperationRecord": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysOperationRecord"
                ],
                "summary": "用id查询SysOperationRecord",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "代理",
                        "name": "agent",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求Body",
                        "name": "body",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "错误信息",
                        "name": "error_message",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求ip",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "延迟",
                        "name": "latency",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求方法",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求路径",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "响应Body",
                        "name": "resp",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "请求状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用id查询SysOperationRecord",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysOperationRecord/getSysOperationRecordList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysOperationRecord"
                ],
                "summary": "分页获取SysOperationRecord列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "代理",
                        "name": "agent",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求Body",
                        "name": "body",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "错误信息",
                        "name": "error_message",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求ip",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "延迟",
                        "name": "latency",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求方法",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "请求路径",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "响应Body",
                        "name": "resp",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "请求状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户id",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取SysOperationRecord列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/createSysParams": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "创建参数",
                "parameters": [
                    {
                        "description": "创建参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysParams"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/deleteSysParams": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "删除参数",
                "parameters": [
                    {
                        "description": "删除参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysParams"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/deleteSysParamsByIds": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "批量删除参数",
                "responses": {
                    "200": {
                        "description": "批量删除成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/findSysParams": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "用id查询参数",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主键ID",
                        "name": "ID",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "创建时间",
                        "name": "createdAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "参数说明",
                        "name": "desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "参数键",
                        "name": "key",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "参数名称",
                        "name": "name",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "更新时间",
                        "name": "updatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "参数值",
                        "name": "value",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/system.SysParams"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/getSysParam": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "根据key获取参数value",
                "parameters": [
                    {
                        "type": "string",
                        "description": "key",
                        "name": "key",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/system.SysParams"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/getSysParamsList": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "分页获取参数列表",
                "parameters": [
                    {
                        "type": "string",
                        "name": "endCreatedAt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "startCreatedAt",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sysParams/updateSysParams": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysParams"
                ],
                "summary": "更新参数",
                "parameters": [
                    {
                        "description": "更新参数",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysParams"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/system/getServerInfo": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "获取服务器信息",
                "responses": {
                    "200": {
                        "description": "获取服务器信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/system/getSystemConfig": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "获取配置文件内容",
                "responses": {
                    "200": {
                        "description": "获取配置文件内容,返回包括系统配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysConfigResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/system/reloadSystem": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "重启系统",
                "responses": {
                    "200": {
                        "description": "重启系统",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/system/setSystemConfig": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System"
                ],
                "summary": "设置配置文件内容",
                "parameters": [
                    {
                        "description": "设置配置文件内容",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.System"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置配置文件内容",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/SetSelfInfo": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "设置用户信息",
                "parameters": [
                    {
                        "description": "ID, 用户名, 昵称, 头像链接",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/SetSelfSetting": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "设置用户配置",
                "parameters": [
                    {
                        "description": "用户配置数据",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置用户配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/admin_register": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "用户注册账号",
                "parameters": [
                    {
                        "description": "用户名, 昵称, 密码, 角色ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.Register"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户注册账号,返回包括用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.SysUserResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/changePassword": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "用户修改密码",
                "parameters": [
                    {
                        "description": "用户名, 原密码, 新密码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.ChangePasswordReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户修改密码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/deleteUser": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "description": "用户ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetById"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除用户",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/getUserInfo": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "获取用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/getUserList": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "分页获取用户列表",
                "parameters": [
                    {
                        "description": "页码, 每页大小",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.GetUserList"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分页获取用户列表,返回包括列表,总数,页码,每页数量",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.PageResult"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/resetPassword": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "重置用户密码",
                "parameters": [
                    {
                        "description": "ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "重置用户密码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/setUserAuthorities": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "设置用户权限",
                "parameters": [
                    {
                        "description": "用户UUID, 角色ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SetUserAuthorities"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置用户权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/setUserAuthority": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "更改用户权限",
                "parameters": [
                    {
                        "description": "用户UUID, 角色ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.SetUserAuth"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置用户权限",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/setUserInfo": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SysUser"
                ],
                "summary": "设置用户信息",
                "parameters": [
                    {
                        "description": "ID, 用户名, 昵称, 头像链接",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/system.SysUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "设置用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "common.JSONMap": {
            "type": "object",
            "additionalProperties": true
        },
        "config.AliyunOSS": {
            "type": "object",
            "properties": {
                "access-key-id": {
                    "type": "string"
                },
                "access-key-secret": {
                    "type": "string"
                },
                "base-path": {
                    "type": "string"
                },
                "bucket-name": {
                    "type": "string"
                },
                "bucket-url": {
                    "type": "string"
                },
                "endpoint": {
                    "type": "string"
                }
            }
        },
        "config.Autocode": {
            "type": "object",
            "properties": {
                "ai-path": {
                    "type": "string"
                },
                "module": {
                    "type": "string"
                },
                "root": {
                    "type": "string"
                },
                "server": {
                    "type": "string"
                },
                "web": {
                    "type": "string"
                }
            }
        },
        "config.AwsS3": {
            "type": "object",
            "properties": {
                "base-url": {
                    "type": "string"
                },
                "bucket": {
                    "type": "string"
                },
                "disable-ssl": {
                    "type": "boolean"
                },
                "endpoint": {
                    "type": "string"
                },
                "path-prefix": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "s3-force-path-style": {
                    "type": "boolean"
                },
                "secret-id": {
                    "type": "string"
                },
                "secret-key": {
                    "type": "string"
                }
            }
        },
        "config.CORS": {
            "type": "object",
            "properties": {
                "mode": {
                    "type": "string"
                },
                "whitelist": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.CORSWhitelist"
                    }
                }
            }
        },
        "config.CORSWhitelist": {
            "type": "object",
            "properties": {
                "allow-credentials": {
                    "type": "boolean"
                },
                "allow-headers": {
                    "type": "string"
                },
                "allow-methods": {
                    "type": "string"
                },
                "allow-origin": {
                    "type": "string"
                },
                "expose-headers": {
                    "type": "string"
                }
            }
        },
        "config.Captcha": {
            "type": "object",
            "properties": {
                "img-height": {
                    "description": "验证码高度",
                    "type": "integer"
                },
                "img-width": {
                    "description": "验证码宽度",
                    "type": "integer"
                },
                "key-long": {
                    "description": "验证码长度",
                    "type": "integer"
                },
                "open-captcha": {
                    "description": "防爆破验证码开启此数，0代表每次登录都需要验证码，其他数字代表错误密码此数，如3代表错误三次后出现验证码",
                    "type": "integer"
                },
                "open-captcha-timeout": {
                    "description": "防爆破验证码超时时间，单位：s(秒)",
                    "type": "integer"
                }
            }
        },
        "config.CloudflareR2": {
            "type": "object",
            "properties": {
                "access-key-id": {
                    "type": "string"
                },
                "account-id": {
                    "type": "string"
                },
                "base-url": {
                    "type": "string"
                },
                "bucket": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "secret-access-key": {
                    "type": "string"
                }
            }
        },
        "config.DiskList": {
            "type": "object",
            "properties": {
                "mount-point": {
                    "type": "string"
                }
            }
        },
        "config.Excel": {
            "type": "object",
            "properties": {
                "dir": {
                    "type": "string"
                }
            }
        },
        "config.HuaWeiObs": {
            "type": "object",
            "properties": {
                "access-key": {
                    "type": "string"
                },
                "bucket": {
                    "type": "string"
                },
                "endpoint": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "secret-key": {
                    "type": "string"
                }
            }
        },
        "config.JWT": {
            "type": "object",
            "properties": {
                "buffer-time": {
                    "description": "缓冲时间",
                    "type": "string"
                },
                "expires-time": {
                    "description": "过期时间",
                    "type": "string"
                },
                "issuer": {
                    "description": "签发者",
                    "type": "string"
                },
                "signing-key": {
                    "description": "jwt签名",
                    "type": "string"
                }
            }
        },
        "config.Local": {
            "type": "object",
            "properties": {
                "path": {
                    "description": "本地文件访问路径",
                    "type": "string"
                },
                "store-path": {
                    "description": "本地文件存储路径",
                    "type": "string"
                }
            }
        },
        "config.Minio": {
            "type": "object",
            "properties": {
                "access-key-id": {
                    "type": "string"
                },
                "access-key-secret": {
                    "type": "string"
                },
                "base-path": {
                    "type": "string"
                },
                "bucket-name": {
                    "type": "string"
                },
                "bucket-url": {
                    "type": "string"
                },
                "endpoint": {
                    "type": "string"
                },
                "use-ssl": {
                    "type": "boolean"
                }
            }
        },
        "config.Mongo": {
            "type": "object",
            "properties": {
                "auth-source": {
                    "description": "验证数据库",
                    "type": "string"
                },
                "coll": {
                    "description": "collection name",
                    "type": "string"
                },
                "connect-timeout-ms": {
                    "description": "连接超时时间",
                    "type": "integer"
                },
                "database": {
                    "description": "database name",
                    "type": "string"
                },
                "hosts": {
                    "description": "主机列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.MongoHost"
                    }
                },
                "is-zap": {
                    "description": "是否开启zap日志",
                    "type": "boolean"
                },
                "max-pool-size": {
                    "description": "最大连接池",
                    "type": "integer"
                },
                "min-pool-size": {
                    "description": "最小连接池",
                    "type": "integer"
                },
                "options": {
                    "description": "mongodb options",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "socket-timeout-ms": {
                    "description": "socket超时时间",
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "config.MongoHost": {
            "type": "object",
            "properties": {
                "host": {
                    "description": "ip地址",
                    "type": "string"
                },
                "port": {
                    "description": "端口",
                    "type": "string"
                }
            }
        },
        "config.Mssql": {
            "type": "object",
            "properties": {
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.Mysql": {
            "type": "object",
            "properties": {
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.Oracle": {
            "type": "object",
            "properties": {
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.Pgsql": {
            "type": "object",
            "properties": {
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.Qiniu": {
            "type": "object",
            "properties": {
                "access-key": {
                    "description": "秘钥AK",
                    "type": "string"
                },
                "bucket": {
                    "description": "空间名称",
                    "type": "string"
                },
                "img-path": {
                    "description": "CDN加速域名",
                    "type": "string"
                },
                "secret-key": {
                    "description": "秘钥SK",
                    "type": "string"
                },
                "use-cdn-domains": {
                    "description": "上传是否使用CDN上传加速",
                    "type": "boolean"
                },
                "use-https": {
                    "description": "是否使用https",
                    "type": "boolean"
                },
                "zone": {
                    "description": "存储区域",
                    "type": "string"
                }
            }
        },
        "config.Redis": {
            "type": "object",
            "properties": {
                "addr": {
                    "description": "服务器地址:端口",
                    "type": "string"
                },
                "clusterAddrs": {
                    "description": "集群模式下的节点地址列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "db": {
                    "description": "单实例模式下redis的哪个数据库",
                    "type": "integer"
                },
                "name": {
                    "description": "代表当前实例的名字",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "useCluster": {
                    "description": "是否使用集群模式",
                    "type": "boolean"
                }
            }
        },
        "config.Server": {
            "type": "object",
            "properties": {
                "aliyun-oss": {
                    "$ref": "#/definitions/config.AliyunOSS"
                },
                "autocode": {
                    "description": "auto",
                    "allOf": [
                        {
                            "$ref": "#/definitions/config.Autocode"
                        }
                    ]
                },
                "aws-s3": {
                    "$ref": "#/definitions/config.AwsS3"
                },
                "captcha": {
                    "$ref": "#/definitions/config.Captcha"
                },
                "cloudflare-r2": {
                    "$ref": "#/definitions/config.CloudflareR2"
                },
                "cors": {
                    "description": "跨域配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/config.CORS"
                        }
                    ]
                },
                "db-list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.SpecializedDB"
                    }
                },
                "disk-list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.DiskList"
                    }
                },
                "email": {
                    "$ref": "#/definitions/github_com_flipped-aurora_gin-vue-admin_server_config.Email"
                },
                "excel": {
                    "$ref": "#/definitions/config.Excel"
                },
                "hua-wei-obs": {
                    "$ref": "#/definitions/config.HuaWeiObs"
                },
                "jwt": {
                    "$ref": "#/definitions/config.JWT"
                },
                "local": {
                    "description": "oss",
                    "allOf": [
                        {
                            "$ref": "#/definitions/config.Local"
                        }
                    ]
                },
                "minio": {
                    "$ref": "#/definitions/config.Minio"
                },
                "mongo": {
                    "$ref": "#/definitions/config.Mongo"
                },
                "mssql": {
                    "$ref": "#/definitions/config.Mssql"
                },
                "mysql": {
                    "description": "gorm",
                    "allOf": [
                        {
                            "$ref": "#/definitions/config.Mysql"
                        }
                    ]
                },
                "oracle": {
                    "$ref": "#/definitions/config.Oracle"
                },
                "pgsql": {
                    "$ref": "#/definitions/config.Pgsql"
                },
                "qiniu": {
                    "$ref": "#/definitions/config.Qiniu"
                },
                "redis": {
                    "$ref": "#/definitions/config.Redis"
                },
                "redis-list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.Redis"
                    }
                },
                "sqlite": {
                    "$ref": "#/definitions/config.Sqlite"
                },
                "system": {
                    "$ref": "#/definitions/config.System"
                },
                "tencent-cos": {
                    "$ref": "#/definitions/config.TencentCOS"
                },
                "zap": {
                    "$ref": "#/definitions/config.Zap"
                }
            }
        },
        "config.SpecializedDB": {
            "type": "object",
            "properties": {
                "alias-name": {
                    "type": "string"
                },
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "disable": {
                    "type": "boolean"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "type": {
                    "type": "string"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.Sqlite": {
            "type": "object",
            "properties": {
                "config": {
                    "description": "高级配置",
                    "type": "string"
                },
                "db-name": {
                    "description": "数据库名",
                    "type": "string"
                },
                "engine": {
                    "description": "数据库引擎，默认InnoDB",
                    "type": "string",
                    "default": "InnoDB"
                },
                "log-mode": {
                    "description": "是否开启Gorm全局日志",
                    "type": "string"
                },
                "log-zap": {
                    "description": "是否通过zap写入日志文件",
                    "type": "boolean"
                },
                "max-idle-conns": {
                    "description": "空闲中的最大连接数",
                    "type": "integer"
                },
                "max-open-conns": {
                    "description": "打开到数据库的最大连接数",
                    "type": "integer"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "path": {
                    "description": "数据库地址",
                    "type": "string"
                },
                "port": {
                    "description": "数据库端口",
                    "type": "string"
                },
                "prefix": {
                    "description": "数据库前缀",
                    "type": "string"
                },
                "singular": {
                    "description": "是否开启全局禁用复数，true表示开启",
                    "type": "boolean"
                },
                "username": {
                    "description": "数据库账号",
                    "type": "string"
                }
            }
        },
        "config.System": {
            "type": "object",
            "properties": {
                "addr": {
                    "description": "端口值",
                    "type": "integer"
                },
                "db-type": {
                    "description": "数据库类型:mysql(默认)|sqlite|sqlserver|postgresql",
                    "type": "string"
                },
                "iplimit-count": {
                    "type": "integer"
                },
                "iplimit-time": {
                    "type": "integer"
                },
                "oss-type": {
                    "description": "Oss类型",
                    "type": "string"
                },
                "router-prefix": {
                    "type": "string"
                },
                "use-mongo": {
                    "description": "使用mongo",
                    "type": "boolean"
                },
                "use-multipoint": {
                    "description": "多点登录拦截",
                    "type": "boolean"
                },
                "use-redis": {
                    "description": "使用redis",
                    "type": "boolean"
                },
                "use-strict-auth": {
                    "description": "使用树形角色分配模式",
                    "type": "boolean"
                }
            }
        },
        "config.TencentCOS": {
            "type": "object",
            "properties": {
                "base-url": {
                    "type": "string"
                },
                "bucket": {
                    "type": "string"
                },
                "path-prefix": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "secret-id": {
                    "type": "string"
                },
                "secret-key": {
                    "type": "string"
                }
            }
        },
        "config.Zap": {
            "type": "object",
            "properties": {
                "director": {
                    "description": "日志文件夹",
                    "type": "string"
                },
                "encode-level": {
                    "description": "编码级",
                    "type": "string"
                },
                "format": {
                    "description": "输出",
                    "type": "string"
                },
                "level": {
                    "description": "级别",
                    "type": "string"
                },
                "log-in-console": {
                    "description": "输出控制台",
                    "type": "boolean"
                },
                "prefix": {
                    "description": "日志前缀",
                    "type": "string"
                },
                "retention-day": {
                    "description": "日志保留天数",
                    "type": "integer"
                },
                "show-line": {
                    "description": "显示行",
                    "type": "boolean"
                },
                "stacktrace-key": {
                    "description": "栈名",
                    "type": "string"
                }
            }
        },
        "example.ExaAttachmentCategory": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/example.ExaAttachmentCategory"
                    }
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "pid": {
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "example.ExaCustomer": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "customerName": {
                    "description": "客户名",
                    "type": "string"
                },
                "customerPhoneData": {
                    "description": "客户手机号",
                    "type": "string"
                },
                "sysUser": {
                    "description": "管理详情",
                    "allOf": [
                        {
                            "$ref": "#/definitions/system.SysUser"
                        }
                    ]
                },
                "sysUserAuthorityID": {
                    "description": "管理角色ID",
                    "type": "integer"
                },
                "sysUserId": {
                    "description": "管理ID",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "example.ExaFile": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "chunkTotal": {
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "exaFileChunk": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/example.ExaFileChunk"
                    }
                },
                "fileMd5": {
                    "type": "string"
                },
                "fileName": {
                    "type": "string"
                },
                "filePath": {
                    "type": "string"
                },
                "isFinish": {
                    "type": "boolean"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "example.ExaFileChunk": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "exaFileID": {
                    "type": "integer"
                },
                "fileChunkNumber": {
                    "type": "integer"
                },
                "fileChunkPath": {
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "example.ExaFileUploadAndDownload": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "classId": {
                    "description": "分类id",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "key": {
                    "description": "编号",
                    "type": "string"
                },
                "name": {
                    "description": "文件名",
                    "type": "string"
                },
                "tag": {
                    "description": "文件标签",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "url": {
                    "description": "文件地址",
                    "type": "string"
                }
            }
        },
        "github_com_flipped-aurora_gin-vue-admin_server_config.Email": {
            "type": "object",
            "properties": {
                "from": {
                    "description": "发件人  你自己要发邮件的邮箱",
                    "type": "string"
                },
                "host": {
                    "description": "服务器地址 例如 smtp.qq.com  请前往QQ或者你要发邮件的邮箱查看其smtp协议",
                    "type": "string"
                },
                "is-ssl": {
                    "description": "是否SSL   是否开启SSL",
                    "type": "boolean"
                },
                "nickname": {
                    "description": "昵称    发件人昵称 通常为自己的邮箱",
                    "type": "string"
                },
                "port": {
                    "description": "端口     请前往QQ或者你要发邮件的邮箱查看其smtp协议 大多为 465",
                    "type": "integer"
                },
                "secret": {
                    "description": "密钥    用于登录的密钥 最好不要用邮箱密码 去邮箱smtp申请一个用于登录的密钥",
                    "type": "string"
                },
                "to": {
                    "description": "收件人:多个以英文逗号分隔 例：<EMAIL> <EMAIL> 正式开发中请把此项目作为参数使用",
                    "type": "string"
                }
            }
        },
        "model.Info": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "attachments": {
                    "description": "附件",
                    "type": "array",
                    "items": {
                        "type": "object"
                    }
                },
                "content": {
                    "description": "内容",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "title": {
                    "description": "标题",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "userID": {
                    "description": "作者",
                    "type": "integer"
                }
            }
        },
        "request.AddMenuAuthorityInfo": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "description": "角色ID",
                    "type": "integer"
                },
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenu"
                    }
                }
            }
        },
        "request.AutoCode": {
            "type": "object",
            "properties": {
                "abbreviation": {
                    "description": "Struct简称",
                    "type": "string",
                    "example": "Struct简称"
                },
                "autoCreateApiToSql": {
                    "description": "是否自动创建api",
                    "type": "boolean",
                    "example": false
                },
                "autoCreateBtnAuth": {
                    "description": "是否自动创建按钮权限",
                    "type": "boolean",
                    "example": false
                },
                "autoCreateMenuToSql": {
                    "description": "是否自动创建menu",
                    "type": "boolean",
                    "example": false
                },
                "autoCreateResource": {
                    "description": "是否自动创建资源标识",
                    "type": "boolean",
                    "example": false
                },
                "autoMigrate": {
                    "description": "是否自动迁移表结构",
                    "type": "boolean",
                    "example": false
                },
                "businessDB": {
                    "description": "业务数据库",
                    "type": "string",
                    "example": "业务数据库"
                },
                "description": {
                    "description": "Struct中文名称",
                    "type": "string",
                    "example": "Struct中文名称"
                },
                "fields": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/request.AutoCodeField"
                    }
                },
                "generateServer": {
                    "description": "是否生成server",
                    "type": "boolean",
                    "example": true
                },
                "generateWeb": {
                    "description": "是否生成web",
                    "type": "boolean",
                    "example": true
                },
                "gvaModel": {
                    "description": "是否使用gva默认Model",
                    "type": "boolean",
                    "example": false
                },
                "humpPackageName": {
                    "description": "go文件名称",
                    "type": "string",
                    "example": "go文件名称"
                },
                "isAdd": {
                    "description": "是否新增",
                    "type": "boolean",
                    "example": false
                },
                "isTree": {
                    "description": "是否树形结构",
                    "type": "boolean",
                    "example": false
                },
                "onlyTemplate": {
                    "description": "是否只生成模板",
                    "type": "boolean",
                    "example": false
                },
                "package": {
                    "type": "string"
                },
                "packageName": {
                    "description": "文件名称",
                    "type": "string",
                    "example": "文件名称"
                },
                "primaryField": {
                    "$ref": "#/definitions/request.AutoCodeField"
                },
                "structName": {
                    "description": "Struct名称",
                    "type": "string",
                    "example": "Struct名称"
                },
                "tableName": {
                    "description": "表名",
                    "type": "string",
                    "example": "表名"
                },
                "treeJson": {
                    "description": "展示的树json字段",
                    "type": "string",
                    "example": "展示的树json字段"
                }
            }
        },
        "request.AutoCodeField": {
            "type": "object",
            "properties": {
                "checkDataSource": {
                    "description": "是否检查数据源",
                    "type": "boolean"
                },
                "clearable": {
                    "description": "是否可清空",
                    "type": "boolean"
                },
                "columnName": {
                    "description": "数据库字段",
                    "type": "string"
                },
                "comment": {
                    "description": "数据库字段描述",
                    "type": "string"
                },
                "dataSource": {
                    "description": "数据源",
                    "allOf": [
                        {
                            "$ref": "#/definitions/request.DataSource"
                        }
                    ]
                },
                "dataTypeLong": {
                    "description": "数据库字段长度",
                    "type": "string"
                },
                "defaultValue": {
                    "description": "是否必填",
                    "type": "string"
                },
                "desc": {
                    "description": "是否前端详情",
                    "type": "boolean"
                },
                "dictType": {
                    "description": "字典",
                    "type": "string"
                },
                "errorText": {
                    "description": "校验失败文字",
                    "type": "string"
                },
                "excel": {
                    "description": "是否导入/导出",
                    "type": "boolean"
                },
                "fieldDesc": {
                    "description": "中文名",
                    "type": "string"
                },
                "fieldIndexType": {
                    "description": "索引类型",
                    "type": "string"
                },
                "fieldJson": {
                    "description": "FieldJson",
                    "type": "string"
                },
                "fieldName": {
                    "description": "Field名",
                    "type": "string"
                },
                "fieldSearchHide": {
                    "description": "是否隐藏查询条件",
                    "type": "boolean"
                },
                "fieldSearchType": {
                    "description": "搜索条件",
                    "type": "string"
                },
                "fieldType": {
                    "description": "Field数据类型",
                    "type": "string"
                },
                "form": {
                    "description": "Front           bool        ` + "`" + `json:\"front\"` + "`" + `           // 是否前端可见",
                    "type": "boolean"
                },
                "primaryKey": {
                    "description": "是否主键",
                    "type": "boolean"
                },
                "require": {
                    "description": "是否必填",
                    "type": "boolean"
                },
                "sort": {
                    "description": "是否增加排序",
                    "type": "boolean"
                },
                "table": {
                    "description": "是否前端表格列",
                    "type": "boolean"
                }
            }
        },
        "request.CasbinInReceive": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "description": "权限id",
                    "type": "integer"
                },
                "casbinInfos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/request.CasbinInfo"
                    }
                }
            }
        },
        "request.CasbinInfo": {
            "type": "object",
            "properties": {
                "method": {
                    "description": "方法",
                    "type": "string"
                },
                "path": {
                    "description": "路径",
                    "type": "string"
                }
            }
        },
        "request.ChangePasswordReq": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "description": "新密码",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                }
            }
        },
        "request.DataSource": {
            "type": "object",
            "properties": {
                "association": {
                    "description": "关联关系 1 一对一 2 一对多",
                    "type": "integer"
                },
                "dbName": {
                    "type": "string"
                },
                "hasDeletedAt": {
                    "type": "boolean"
                },
                "label": {
                    "type": "string"
                },
                "table": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "request.Empty": {
            "type": "object"
        },
        "request.ExaAttachmentCategorySearch": {
            "type": "object",
            "properties": {
                "classId": {
                    "type": "integer"
                },
                "keyword": {
                    "description": "关键字",
                    "type": "string"
                },
                "page": {
                    "description": "页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页大小",
                    "type": "integer"
                }
            }
        },
        "request.GetAuthorityId": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "description": "角色ID",
                    "type": "integer"
                }
            }
        },
        "request.GetById": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                }
            }
        },
        "request.GetUserList": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "keyword": {
                    "description": "关键字",
                    "type": "string"
                },
                "nickName": {
                    "type": "string"
                },
                "page": {
                    "description": "页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页大小",
                    "type": "integer"
                },
                "phone": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "request.IdsReq": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "request.InitDB": {
            "type": "object",
            "required": [
                "adminPassword",
                "dbName"
            ],
            "properties": {
                "adminPassword": {
                    "type": "string"
                },
                "dbName": {
                    "description": "数据库名",
                    "type": "string"
                },
                "dbPath": {
                    "description": "sqlite数据库文件路径",
                    "type": "string"
                },
                "dbType": {
                    "description": "数据库类型",
                    "type": "string"
                },
                "host": {
                    "description": "服务器地址",
                    "type": "string"
                },
                "password": {
                    "description": "数据库密码",
                    "type": "string"
                },
                "port": {
                    "description": "数据库连接端口",
                    "type": "string"
                },
                "template": {
                    "description": "postgresql指定template",
                    "type": "string"
                },
                "userName": {
                    "description": "数据库用户名",
                    "type": "string"
                }
            }
        },
        "request.Login": {
            "type": "object",
            "properties": {
                "captcha": {
                    "description": "验证码",
                    "type": "string"
                },
                "captchaId": {
                    "description": "验证码ID",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "request.PageInfo": {
            "type": "object",
            "properties": {
                "keyword": {
                    "description": "关键字",
                    "type": "string"
                },
                "page": {
                    "description": "页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页大小",
                    "type": "integer"
                }
            }
        },
        "request.Register": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "type": "string",
                    "example": "int 角色id"
                },
                "authorityIds": {
                    "type": "string",
                    "example": "[]uint 角色id"
                },
                "email": {
                    "type": "string",
                    "example": "电子邮箱"
                },
                "enable": {
                    "type": "string",
                    "example": "int 是否启用"
                },
                "headerImg": {
                    "type": "string",
                    "example": "头像链接"
                },
                "nickName": {
                    "type": "string",
                    "example": "昵称"
                },
                "passWord": {
                    "type": "string",
                    "example": "密码"
                },
                "phone": {
                    "type": "string",
                    "example": "电话号码"
                },
                "userName": {
                    "type": "string",
                    "example": "用户名"
                }
            }
        },
        "request.SearchApiParams": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "apiGroup": {
                    "description": "api组",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "desc": {
                    "description": "排序方式:升序false(默认)|降序true",
                    "type": "boolean"
                },
                "description": {
                    "description": "api中文描述",
                    "type": "string"
                },
                "keyword": {
                    "description": "关键字",
                    "type": "string"
                },
                "method": {
                    "description": "方法:创建POST(默认)|查看GET|更新PUT|删除DELETE",
                    "type": "string"
                },
                "orderKey": {
                    "description": "排序",
                    "type": "string"
                },
                "page": {
                    "description": "页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页大小",
                    "type": "integer"
                },
                "path": {
                    "description": "api路径",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "request.SetUserAuth": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "description": "角色ID",
                    "type": "integer"
                }
            }
        },
        "request.SetUserAuthorities": {
            "type": "object",
            "properties": {
                "authorityIds": {
                    "description": "角色ID",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "request.SysAuthorityBtnReq": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "type": "integer"
                },
                "menuID": {
                    "type": "integer"
                },
                "selected": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "request.SysAutoCodePackageCreate": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string",
                    "example": "描述"
                },
                "label": {
                    "type": "string",
                    "example": "展示名"
                },
                "packageName": {
                    "type": "string",
                    "example": "包名"
                },
                "template": {
                    "type": "string",
                    "example": "模版"
                }
            }
        },
        "request.SysAutoHistoryRollBack": {
            "type": "object",
            "properties": {
                "deleteApi": {
                    "description": "是否删除接口",
                    "type": "boolean"
                },
                "deleteMenu": {
                    "description": "是否删除菜单",
                    "type": "boolean"
                },
                "deleteTable": {
                    "description": "是否删除表",
                    "type": "boolean"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                }
            }
        },
        "response.Email": {
            "type": "object",
            "properties": {
                "body": {
                    "description": "邮件内容",
                    "type": "string"
                },
                "subject": {
                    "description": "邮件标题",
                    "type": "string"
                },
                "to": {
                    "description": "邮件发送给谁",
                    "type": "string"
                }
            }
        },
        "response.ExaCustomerResponse": {
            "type": "object",
            "properties": {
                "customer": {
                    "$ref": "#/definitions/example.ExaCustomer"
                }
            }
        },
        "response.ExaFileResponse": {
            "type": "object",
            "properties": {
                "file": {
                    "$ref": "#/definitions/example.ExaFileUploadAndDownload"
                }
            }
        },
        "response.FilePathResponse": {
            "type": "object",
            "properties": {
                "filePath": {
                    "type": "string"
                }
            }
        },
        "response.FileResponse": {
            "type": "object",
            "properties": {
                "file": {
                    "$ref": "#/definitions/example.ExaFile"
                }
            }
        },
        "response.LoginResponse": {
            "type": "object",
            "properties": {
                "expiresAt": {
                    "type": "integer"
                },
                "token": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/system.SysUser"
                }
            }
        },
        "response.PageResult": {
            "type": "object",
            "properties": {
                "list": {},
                "page": {
                    "type": "integer"
                },
                "pageSize": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "response.PolicyPathResponse": {
            "type": "object",
            "properties": {
                "paths": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/request.CasbinInfo"
                    }
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "msg": {
                    "type": "string"
                }
            }
        },
        "response.SysAPIListResponse": {
            "type": "object",
            "properties": {
                "apis": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysApi"
                    }
                }
            }
        },
        "response.SysAPIResponse": {
            "type": "object",
            "properties": {
                "api": {
                    "$ref": "#/definitions/system.SysApi"
                }
            }
        },
        "response.SysAuthorityBtnRes": {
            "type": "object",
            "properties": {
                "selected": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "response.SysAuthorityCopyResponse": {
            "type": "object",
            "properties": {
                "authority": {
                    "$ref": "#/definitions/system.SysAuthority"
                },
                "oldAuthorityId": {
                    "description": "旧角色ID",
                    "type": "integer"
                }
            }
        },
        "response.SysAuthorityResponse": {
            "type": "object",
            "properties": {
                "authority": {
                    "$ref": "#/definitions/system.SysAuthority"
                }
            }
        },
        "response.SysBaseMenuResponse": {
            "type": "object",
            "properties": {
                "menu": {
                    "$ref": "#/definitions/system.SysBaseMenu"
                }
            }
        },
        "response.SysBaseMenusResponse": {
            "type": "object",
            "properties": {
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenu"
                    }
                }
            }
        },
        "response.SysCaptchaResponse": {
            "type": "object",
            "properties": {
                "captchaId": {
                    "type": "string"
                },
                "captchaLength": {
                    "type": "integer"
                },
                "openCaptcha": {
                    "type": "boolean"
                },
                "picPath": {
                    "type": "string"
                }
            }
        },
        "response.SysConfigResponse": {
            "type": "object",
            "properties": {
                "config": {
                    "$ref": "#/definitions/config.Server"
                }
            }
        },
        "response.SysMenusResponse": {
            "type": "object",
            "properties": {
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysMenu"
                    }
                }
            }
        },
        "response.SysUserResponse": {
            "type": "object",
            "properties": {
                "user": {
                    "$ref": "#/definitions/system.SysUser"
                }
            }
        },
        "system.Condition": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "column": {
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "from": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "templateID": {
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.JoinTemplate": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "joins": {
                    "type": "string"
                },
                "on": {
                    "type": "string"
                },
                "table": {
                    "type": "string"
                },
                "templateID": {
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.Meta": {
            "type": "object",
            "properties": {
                "activeName": {
                    "type": "string"
                },
                "closeTab": {
                    "description": "自动关闭tab",
                    "type": "boolean"
                },
                "defaultMenu": {
                    "description": "是否是基础路由（开发中）",
                    "type": "boolean"
                },
                "icon": {
                    "description": "菜单图标",
                    "type": "string"
                },
                "keepAlive": {
                    "description": "是否缓存",
                    "type": "boolean"
                },
                "title": {
                    "description": "菜单名",
                    "type": "string"
                }
            }
        },
        "system.SysApi": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "apiGroup": {
                    "description": "api组",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "api中文描述",
                    "type": "string"
                },
                "method": {
                    "description": "方法:创建POST(默认)|查看GET|更新PUT|删除DELETE",
                    "type": "string"
                },
                "path": {
                    "description": "api路径",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysAuthority": {
            "type": "object",
            "properties": {
                "authorityId": {
                    "description": "角色ID",
                    "type": "integer"
                },
                "authorityName": {
                    "description": "角色名",
                    "type": "string"
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysAuthority"
                    }
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "dataAuthorityId": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysAuthority"
                    }
                },
                "defaultRouter": {
                    "description": "默认菜单(默认dashboard)",
                    "type": "string"
                },
                "deletedAt": {
                    "type": "string"
                },
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenu"
                    }
                },
                "parentId": {
                    "description": "父角色ID",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysBaseMenu": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "authoritys": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysAuthority"
                    }
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenu"
                    }
                },
                "component": {
                    "description": "对应前端文件路径",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "hidden": {
                    "description": "是否在列表隐藏",
                    "type": "boolean"
                },
                "menuBtn": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenuBtn"
                    }
                },
                "meta": {
                    "description": "附加属性",
                    "allOf": [
                        {
                            "$ref": "#/definitions/system.Meta"
                        }
                    ]
                },
                "name": {
                    "description": "路由name",
                    "type": "string"
                },
                "parameters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenuParameter"
                    }
                },
                "parentId": {
                    "description": "父菜单ID",
                    "type": "integer"
                },
                "path": {
                    "description": "路由path",
                    "type": "string"
                },
                "sort": {
                    "description": "排序标记",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysBaseMenuBtn": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "desc": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "sysBaseMenuID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysBaseMenuParameter": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "key": {
                    "description": "地址栏携带参数的key",
                    "type": "string"
                },
                "sysBaseMenuID": {
                    "type": "integer"
                },
                "type": {
                    "description": "地址栏携带参数为params还是query",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "value": {
                    "description": "地址栏携带参数的值",
                    "type": "string"
                }
            }
        },
        "system.SysDictionary": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "desc": {
                    "description": "描述",
                    "type": "string"
                },
                "name": {
                    "description": "字典名（中）",
                    "type": "string"
                },
                "status": {
                    "description": "状态",
                    "type": "boolean"
                },
                "sysDictionaryDetails": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysDictionaryDetail"
                    }
                },
                "type": {
                    "description": "字典名（英）",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysDictionaryDetail": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "extend": {
                    "description": "扩展值",
                    "type": "string"
                },
                "label": {
                    "description": "展示值",
                    "type": "string"
                },
                "sort": {
                    "description": "排序标记",
                    "type": "integer"
                },
                "status": {
                    "description": "启用状态",
                    "type": "boolean"
                },
                "sysDictionaryID": {
                    "description": "关联标记",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "value": {
                    "description": "字典值",
                    "type": "string"
                }
            }
        },
        "system.SysExportTemplate": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "conditions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.Condition"
                    }
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "dbName": {
                    "description": "数据库名称",
                    "type": "string"
                },
                "joinTemplate": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.JoinTemplate"
                    }
                },
                "limit": {
                    "type": "integer"
                },
                "name": {
                    "description": "模板名称",
                    "type": "string"
                },
                "order": {
                    "type": "string"
                },
                "tableName": {
                    "description": "表名称",
                    "type": "string"
                },
                "templateID": {
                    "description": "模板标识",
                    "type": "string"
                },
                "templateInfo": {
                    "description": "模板信息",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysMenu": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "authoritys": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysAuthority"
                    }
                },
                "btns": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysMenu"
                    }
                },
                "component": {
                    "description": "对应前端文件路径",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "hidden": {
                    "description": "是否在列表隐藏",
                    "type": "boolean"
                },
                "menuBtn": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenuBtn"
                    }
                },
                "menuId": {
                    "type": "integer"
                },
                "meta": {
                    "description": "附加属性",
                    "allOf": [
                        {
                            "$ref": "#/definitions/system.Meta"
                        }
                    ]
                },
                "name": {
                    "description": "路由name",
                    "type": "string"
                },
                "parameters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysBaseMenuParameter"
                    }
                },
                "parentId": {
                    "description": "父菜单ID",
                    "type": "integer"
                },
                "path": {
                    "description": "路由path",
                    "type": "string"
                },
                "sort": {
                    "description": "排序标记",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "system.SysOperationRecord": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "agent": {
                    "description": "代理",
                    "type": "string"
                },
                "body": {
                    "description": "请求Body",
                    "type": "string"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "error_message": {
                    "description": "错误信息",
                    "type": "string"
                },
                "ip": {
                    "description": "请求ip",
                    "type": "string"
                },
                "latency": {
                    "description": "延迟",
                    "type": "string"
                },
                "method": {
                    "description": "请求方法",
                    "type": "string"
                },
                "path": {
                    "description": "请求路径",
                    "type": "string"
                },
                "resp": {
                    "description": "响应Body",
                    "type": "string"
                },
                "status": {
                    "description": "请求状态",
                    "type": "integer"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/system.SysUser"
                },
                "user_id": {
                    "description": "用户id",
                    "type": "integer"
                }
            }
        },
        "system.SysParams": {
            "type": "object",
            "required": [
                "key",
                "name",
                "value"
            ],
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "desc": {
                    "description": "参数说明",
                    "type": "string"
                },
                "key": {
                    "description": "参数键",
                    "type": "string"
                },
                "name": {
                    "description": "参数名称",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "value": {
                    "description": "参数值",
                    "type": "string"
                }
            }
        },
        "system.SysUser": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "authorities": {
                    "description": "多用户角色",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/system.SysAuthority"
                    }
                },
                "authority": {
                    "description": "用户角色",
                    "allOf": [
                        {
                            "$ref": "#/definitions/system.SysAuthority"
                        }
                    ]
                },
                "authorityId": {
                    "description": "用户角色ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "email": {
                    "description": "用户邮箱",
                    "type": "string"
                },
                "enable": {
                    "description": "用户是否被冻结 1正常 2冻结",
                    "type": "integer"
                },
                "headerImg": {
                    "description": "用户头像",
                    "type": "string"
                },
                "nickName": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "originSetting": {
                    "description": "配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/common.JSONMap"
                        }
                    ]
                },
                "phone": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "userName": {
                    "description": "用户登录名",
                    "type": "string"
                },
                "uuid": {
                    "description": "用户UUID",
                    "type": "string"
                }
            }
        },
        "system.System": {
            "type": "object",
            "properties": {
                "config": {
                    "$ref": "#/definitions/config.Server"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "x-token",
            "in": "header"
        }
    },
    "tags": [
        {
            "name": "Base"
        },
        {
            "description": "用户",
            "name": "SysUser"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "v2.8.4",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Gin-Vue-Admin Swagger API接口文档",
	Description:      "使用gin+vue进行极速开发的全栈开发基础平台",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
